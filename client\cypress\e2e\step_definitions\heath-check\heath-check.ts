import { Before, Given } from '@badeball/cypress-cucumber-preprocessor';
import { Graphql } from '../../../support/helperFunction/caseManagerHelper';
import { SettingsGraphql } from '../../../support/helperFunction/settingsHelper';

Before({ tags: '@health-check' }, () => {
  cy.LoginToApp();
  cy.interceptGraphQLQuery(Graphql.FetchCaseStatuses, 'fetchCaseStatuses');
  cy.interceptGraphQLQuery(Graphql.FetchTags, 'fetchTags');
  cy.interceptGraphQLQuery(Graphql.FetchDetailPopup, 'fetchDetailPopup');
  cy.interceptGraphQLQuery(Graphql.FetchCases, 'fetchCaseList');
  cy.intercept('PUT', /s3.amazonaws.com/).as('uploadFile');
});

Given('The user logins to Investigate-App successfully', () => {
  cy.LoginLandingPage();
});

Given('The user verify Case Management screen', () => {
  cy.visit('/');
  cy.get('body').should(($body) => {
    expect($body.text()).to.match(/Case Management/);
  });
  cy.url().should('include', 'case-manager');
  cy.awaitNetworkResponseCode({ alias: '@fetchCaseStatuses', code: 200 });
  cy.awaitNetworkResponseCode({ alias: '@fetchTags', code: 200 });
});

Given('The user verify Settings screen', () => {
  cy.interceptGraphQLQuery(
    SettingsGraphql.FetchSettingStatuses,
    'fetchStatusCode'
  );
  cy.interceptGraphQLQuery(SettingsGraphql.DeleteQuery, 'deleteStatus');
  cy.visit('/settings');
  cy.get('body').should(($body) => {
    expect($body.text()).to.match(/Settings/);
  });
  cy.url().should('include', 'settings');
});

Given('The user verify Tag Settings screen', () => {
  cy.getDataIdCy({ idAlias: 'preconfigured-tags-tab' }).click();
  cy.get('.settings__option-header').should(
    'contain.text',
    'Case Tag Settings'
  );
});
